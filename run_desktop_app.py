#!/usr/bin/env python3
"""
Simple launcher for the AI Video Search Desktop Application.
Double-click this file to start the application.
"""

import sys
import os
import subprocess
from pathlib import Path

def check_requirements():
    """Check if required packages are installed."""
    required_packages = [
        'tkinter',
        'PIL',
        'cv2',
        'torch',
        'transformers'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            if package == 'tkinter':
                import tkinter
            elif package == 'PIL':
                from PIL import Image
            elif package == 'cv2':
                import cv2
            elif package == 'torch':
                import torch
            elif package == 'transformers':
                import transformers
        except ImportError:
            missing_packages.append(package)
    
    return missing_packages

def main():
    """Main launcher function."""
    print("🚀 AI Video Search Desktop Application Launcher")
    print("=" * 50)
    
    # Check if we're in the right directory
    if not os.path.exists("desktop_app.py"):
        print("❌ Error: desktop_app.py not found!")
        print("💡 Make sure you're running this from the correct directory")
        input("Press Enter to exit...")
        return
    
    # Check requirements
    print("🔍 Checking requirements...")
    missing = check_requirements()
    
    if missing:
        print(f"❌ Missing required packages: {', '.join(missing)}")
        print("💡 Please install missing packages:")
        print("   pip install -r requirements.txt")
        input("Press Enter to exit...")
        return
    
    print("✅ All requirements satisfied!")
    
    # Launch the desktop application
    print("🎬 Starting desktop application...")
    
    try:
        # Run the desktop app
        subprocess.run([sys.executable, "desktop_app.py"], check=True)
    except subprocess.CalledProcessError as e:
        print(f"❌ Error running desktop app: {e}")
        input("Press Enter to exit...")
    except KeyboardInterrupt:
        print("\n👋 Application stopped by user")
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        input("Press Enter to exit...")

if __name__ == "__main__":
    main()

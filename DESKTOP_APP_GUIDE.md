# 🎬 AI Video Search Desktop Application

A standalone desktop GUI application for AI-powered video content search with an intuitive interface.

## 🚀 Quick Start

### Method 1: Double-Click Launch (Easiest)
1. **Windows Users**: Double-click `Start_AI_Video_Search.bat`
2. **All Platforms**: Double-click `run_desktop_app.py`

### Method 2: Command Line
```bash
python desktop_app.py
```

## 📱 Application Features

### 🎯 Main Tabs

#### 1. 📁 Video Search Tab
- **File Selection**: Browse and select video files (MP4, AVI, MOV, etc.)
- **Search Query**: Enter what you want to find (e.g., "person", "red car", "dog")
- **Advanced Parameters**:
  - Similarity Threshold: Adjust sensitivity (0.1 = more results, 0.5 = fewer, more accurate)
  - Max Results: Limit number of matches
  - Create Clips: Generate video clips of matches
  - Create Thumbnails: Generate thumbnail images
- **Real-time Progress**: Progress bar and status updates

#### 2. 📹 Live Detection Tab
- **Webcam Detection**: Real-time object detection using your camera
- **Live Parameters**:
  - Detection Interval: How often to check (0.1-2.0 seconds)
  - Camera Selection: Choose camera if multiple available
- **Controls**: Start, Pause/Resume, Stop
- **Live Results**: Real-time detection results display

#### 3. ⚙️ Settings Tab
- **Model Settings**: Choose CLIP model (base/large/etc.)
- **Performance Settings**: 
  - GPU acceleration toggle
  - Parallel processing options
- **Frame Extraction**: Adjust frame sampling interval
- **Save Settings**: Persist your preferences

#### 4. 📊 Results Tab
- **Detailed Results**: Complete search results with timestamps
- **Export Options**: Save results to text/CSV files
- **Folder Access**: Open results folder directly
- **Clear Results**: Clean up display

## 🎮 How to Use

### Video Search Workflow
1. **Select Video**: Click "Browse" to choose your video file
2. **Enter Query**: Type what you're looking for (e.g., "person walking")
3. **Adjust Settings**: 
   - Lower threshold for more results
   - Higher threshold for more accurate results
4. **Start Search**: Click "🔍 Start Search"
5. **View Results**: Results appear in real-time, switch to Results tab for details
6. **Access Files**: Click "📁 Open Results Folder" to see generated clips/thumbnails

### Live Detection Workflow
1. **Enter Query**: Type what to detect (e.g., "bottle", "phone")
2. **Select Camera**: Choose camera index (usually 0 for default)
3. **Start Detection**: Click "▶️ Start Live Detection"
4. **Monitor Results**: Watch live detection results
5. **Control**: Use Pause/Resume or Stop as needed

### Settings Configuration
1. **Go to Settings Tab**
2. **Adjust Parameters**:
   - Choose appropriate CLIP model for your needs
   - Enable GPU if available
   - Adjust frame interval (lower = more thorough, slower)
3. **Save Settings**: Click "💾 Save Settings"

## 🔧 Tips for Best Results

### Search Query Tips
- **Be Specific**: "red car" vs "car"
- **Use Simple Terms**: "person walking" vs "pedestrian locomotion"
- **Try Variations**: "dog", "puppy", "canine"
- **Objects Work Best**: Physical objects, people, animals

### Performance Optimization
- **Frame Interval**: 
  - 15-20: High quality, slower
  - 30-45: Balanced
  - 60+: Fast, lower quality
- **Similarity Threshold**:
  - 0.15-0.2: More results, some false positives
  - 0.25-0.3: Balanced
  - 0.35+: Fewer, more accurate results

### Troubleshooting
- **No Results Found**: Try lowering similarity threshold
- **Too Many Results**: Increase similarity threshold or be more specific
- **Slow Performance**: Increase frame interval, disable GPU if causing issues
- **Camera Issues**: Try different camera index (0, 1, 2)

## 📁 Output Files

### Generated Files
- **Thumbnails**: `static/output_clips/thumbnails/`
- **Video Clips**: `static/output_clips/clips/`
- **Logs**: `logs/app.log`

### File Naming
- Thumbnails: `query_frame_XXXX_score_X.XX.jpg`
- Clips: `query_clip_XXXX_score_X.XX.mp4`

## ⚡ Keyboard Shortcuts

- **Ctrl+O**: Browse for video file (when in Video Search tab)
- **Enter**: Start search (when query field is focused)
- **Escape**: Stop current operation
- **F5**: Refresh/reload settings

## 🔍 Advanced Features

### Batch Processing
- Use the Results tab to track multiple searches
- Export results for analysis
- Open results folder for batch file operations

### Live Detection Features
- Pause detection without stopping camera
- Configurable detection intervals
- Object extraction and recognition
- Real-time similarity scoring

### Configuration Management
- Settings persist between sessions
- Export/import configuration
- Multiple model support
- Performance profiling

## 🆘 Common Issues

### Application Won't Start
```bash
# Check Python installation
python --version

# Check required packages
python -c "import tkinter, cv2, torch, transformers; print('All packages available')"

# Install missing packages
pip install -r requirements.txt
```

### Video File Issues
- **Supported Formats**: MP4, AVI, MOV, MKV, WMV, FLV, WebM
- **Large Files**: Use lower frame intervals for faster processing
- **Corrupted Files**: Try with a different video file

### Camera Issues
- **Permission**: Grant camera access to Python
- **Multiple Cameras**: Try different camera indices (0, 1, 2)
- **Driver Issues**: Update camera drivers

## 🎯 Example Use Cases

### Security Monitoring
- Search for "person" in security footage
- Detect "vehicle" in parking lot videos
- Find "package" deliveries

### Content Analysis
- Find "logo" appearances in videos
- Detect "product" placements
- Search for "text" or "signs"

### Personal Videos
- Find "dog" or "cat" moments
- Search for "birthday" or "celebration"
- Locate "outdoor" vs "indoor" scenes

## 📞 Support

If you encounter issues:
1. Check the logs in `logs/app.log`
2. Try the troubleshooting steps above
3. Restart the application
4. Check system requirements

---

**Enjoy using the AI Video Search Desktop Application! 🎉**

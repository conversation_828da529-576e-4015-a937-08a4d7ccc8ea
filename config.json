{"models": {"clip_model_name": "openai/clip-vit-base-patch32", "yolo_model_name": "yolov8n.pt", "device": "auto", "model_cache_dir": "models/cache", "enable_model_optimization": true, "mixed_precision": true}, "processing": {"default_frame_interval": 30, "default_target_resolution": [512, 384], "default_quality_factor": 0.8, "max_video_size_gb": 10.0, "supported_formats": [".mp4", ".avi", ".mov", ".mkv", ".wmv", ".flv", ".webm"], "chunk_size": 200, "use_chunked_processing": true, "enable_gpu_acceleration": true}, "search": {"default_similarity_threshold": 0.2, "advanced_similarity_threshold": 0.25, "default_max_results": 20, "default_clip_duration": 3.0, "enable_advanced_matching": true, "enable_object_extraction": true, "confidence_threshold": 0.5}, "output": {"output_dir": "static/output_clips", "temp_dir": "temp_videos", "test_output_dir": "test_output", "default_image_quality": 90, "default_thumbnail_size": [480, 360], "cleanup_on_exit": true, "save_metadata": true}, "memory": {"max_cache_size_mb": 2048, "enable_memory_monitoring": true, "auto_cleanup_threshold": 0.8, "frame_cache_limit": 1000, "enable_garbage_collection": true}, "performance": {"enable_gpu": true, "batch_size": 4, "num_workers": 4, "enable_mixed_precision": true, "enable_parallel_processing": true, "max_workers": 4}, "live_detection": {"detection_interval": 0.5, "max_live_results": 20, "enable_pause_resume": true, "save_detections": true, "detection_confidence_threshold": 0.5, "enable_object_tracking": false}, "web_interface": {"streamlit_port": 8501, "streamlit_host": "localhost", "enable_file_upload": true, "max_upload_size_mb": 500, "enable_live_detection": true, "theme": "light", "enable_dark_mode": true}, "logging": {"level": "INFO", "log_file": "logs/app.log", "enable_file_logging": true, "enable_console_logging": true, "max_log_size_mb": 100, "backup_count": 5}}
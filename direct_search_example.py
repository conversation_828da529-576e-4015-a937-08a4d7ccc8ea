#!/usr/bin/env python3
"""
Example of using the video search engine directly in Python.
"""

import sys
import os
from pathlib import Path

# Add project root to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.clip_match import VideoSearchEngine
from config_advanced import AdvancedConfig

def direct_search_example():
    """Example of direct API usage."""
    
    # Initialize configuration
    config = AdvancedConfig()
    
    # Create search engine
    search_engine = VideoSearchEngine(
        clip_model_name=config.models.clip_model_name,
        frame_interval=config.processing.default_frame_interval,
        target_resolution=config.processing.default_target_resolution,
        enable_parallel_processing=config.performance.enable_parallel_processing,
        max_workers=config.performance.max_workers
    )
    
    # Video file and query
    video_path = "temp_videos/202502252001_2.mp4"  # Change this to your video
    query = "person"  # Change this to your search term
    
    if not os.path.exists(video_path):
        print(f"❌ Video file not found: {video_path}")
        print("💡 Please update the video_path variable with a valid video file")
        return
    
    print(f"🎬 Searching video: {video_path}")
    print(f"🔍 Query: '{query}'")
    print("-" * 50)
    
    # Perform search
    try:
        results = search_engine.search_video(
            video_path=video_path,
            query=query,
            similarity_threshold=0.2,
            top_k=10,
            create_clips=True,
            create_thumbnails=True,
            use_advanced_matching=True
        )
        
        # Display results
        if results['success']:
            matches = results['matches']
            print(f"✅ Search completed successfully!")
            print(f"🎯 Found {len(matches)} matches")
            print(f"⏱️  Processing time: {results['processing_time']:.2f} seconds")
            
            if matches:
                print("\n📋 Match Details:")
                for i, match in enumerate(matches, 1):
                    timestamp = match['timestamp']
                    score = match['similarity_score']
                    frame_idx = match['frame_index']
                    
                    minutes = int(timestamp // 60)
                    seconds = int(timestamp % 60)
                    
                    print(f"  {i:2d}. Frame {frame_idx:4d} at {minutes:02d}:{seconds:02d} (score: {score:.3f})")
                
                # Show file locations
                if results.get('clips'):
                    print(f"\n📹 Video clips saved to: {results['output_info']['clips_dir']}")
                
                if results.get('thumbnails'):
                    print(f"🖼️  Thumbnails saved to: {results['output_info']['thumbnails_dir']}")
            else:
                print("❌ No matches found. Try:")
                print("   - Lowering the similarity threshold")
                print("   - Using different search terms")
                print("   - Checking if the content actually exists in the video")
        
        else:
            print(f"❌ Search failed: {results.get('error', 'Unknown error')}")
    
    except Exception as e:
        print(f"❌ Error during search: {e}")
        import traceback
        traceback.print_exc()

def search_multiple_queries():
    """Example of searching for multiple queries in the same video."""
    
    config = AdvancedConfig()
    search_engine = VideoSearchEngine(
        frame_interval=config.processing.default_frame_interval,
        enable_parallel_processing=True
    )
    
    video_path = "temp_videos/202502252001_2.mp4"  # Change this
    queries = ["person", "car", "building", "tree", "sky"]  # Add your queries
    
    if not os.path.exists(video_path):
        print(f"❌ Video file not found: {video_path}")
        return
    
    print(f"🎬 Video: {video_path}")
    print(f"🔍 Searching for {len(queries)} different queries...")
    print("=" * 60)
    
    all_results = {}
    
    for query in queries:
        print(f"\n🔍 Searching for: '{query}'")
        
        try:
            results = search_engine.search_video(
                video_path=video_path,
                query=query,
                similarity_threshold=0.2,
                top_k=5,
                create_clips=False,  # Skip clips for faster processing
                create_thumbnails=True
            )
            
            if results['success']:
                match_count = len(results['matches'])
                all_results[query] = match_count
                print(f"   ✅ Found {match_count} matches")
            else:
                all_results[query] = 0
                print(f"   ❌ Search failed")
                
        except Exception as e:
            all_results[query] = 0
            print(f"   ❌ Error: {e}")
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 MULTI-QUERY SEARCH SUMMARY")
    print("=" * 60)
    
    total_matches = 0
    for query, count in all_results.items():
        print(f"{query:<15} {count:>3} matches")
        total_matches += count
    
    print("-" * 30)
    print(f"{'TOTAL':<15} {total_matches:>3} matches")

if __name__ == "__main__":
    print("🚀 AI Video Search - Direct API Example")
    print("=" * 50)
    
    # Run single search example
    direct_search_example()
    
    # Uncomment to run multi-query example
    # print("\n" + "=" * 50)
    # search_multiple_queries()

#!/usr/bin/env python3
"""
Test script specifically for the results display functionality.
"""

import sys
import os
import tkinter as tk
import time

# Add project root to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_results_display():
    """Test the results display functionality."""
    print("🧪 Testing Results Display Functionality")
    print("=" * 50)
    
    try:
        import desktop_app
        
        # Create hidden test window
        root = tk.Tk()
        root.withdraw()
        
        # Create app
        app = desktop_app.VideoSearchGUI(root)
        
        if app.config is None:
            print("❌ App failed to initialize")
            return False
        
        print("✅ App initialized successfully")
        
        # Test 1: Basic logging
        print("\n📝 Test 1: Basic Logging")
        app.log_message("Test message 1")
        app.log_message("✅ Test success message")
        app.log_message("❌ Test error message")
        print("✅ Basic logging works")
        
        # Test 2: Enhanced logging with timestamps
        print("\n⏰ Test 2: Enhanced Logging")
        app.log_message("🎯 Found 3 matches in 12.34 seconds")
        app.log_message("📊 Processing completed")
        print("✅ Enhanced logging with timestamps works")
        
        # Test 3: Mock search results
        print("\n🔍 Test 3: Mock Search Results")
        mock_results = {
            'success': True,
            'matches': [
                {'frame_index': 100, 'timestamp': 65.5, 'similarity_score': 0.85},
                {'frame_index': 200, 'timestamp': 130.2, 'similarity_score': 0.72},
                {'frame_index': 300, 'timestamp': 195.8, 'similarity_score': 0.68}
            ],
            'processing_time': 15.3,
            'output_info': {
                'clips_dir': 'static/output_clips/clips',
                'thumbnails_dir': 'static/output_clips/thumbnails'
            },
            'clips': ['clip1.mp4', 'clip2.mp4', 'clip3.mp4'],
            'thumbnails': ['thumb1.jpg', 'thumb2.jpg', 'thumb3.jpg'],
            'video_info': {
                'duration': 300.5,
                'fps': 29.97,
                'total_frames': 9000
            }
        }
        
        app.handle_search_results(mock_results)
        print("✅ Mock search results processed successfully")
        
        # Test 4: Results summary
        print("\n📊 Test 4: Results Summary")
        if hasattr(app, 'results_summary_var'):
            summary = app.results_summary_var.get()
            print(f"   Summary: {summary}")
            print("✅ Results summary updated")
        else:
            print("⚠️ Results summary variable not found")
        
        # Test 5: Refresh functionality
        print("\n🔄 Test 5: Refresh Functionality")
        app.refresh_results_view()
        print("✅ Results refresh works")
        
        # Test 6: Progress updates
        print("\n⏳ Test 6: Progress Updates")
        app.progress_var.set("🔍 Testing progress update...")
        progress_text = app.progress_var.get()
        print(f"   Progress: {progress_text}")
        print("✅ Progress updates work")
        
        # Test 7: Error handling
        print("\n❌ Test 7: Error Handling")
        error_results = {
            'success': False,
            'error': 'Test error message'
        }
        app.handle_search_results(error_results)
        print("✅ Error handling works")
        
        # Test 8: No results case
        print("\n🚫 Test 8: No Results Case")
        no_results = {
            'success': True,
            'matches': [],
            'processing_time': 5.2
        }
        app.handle_search_results(no_results)
        print("✅ No results case handled")
        
        # Test 9: GUI components
        print("\n🖥️ Test 9: GUI Components")
        components = [
            'results_text', 'live_results_text', 'progress_var', 
            'results_summary_var', 'notebook', 'search_button'
        ]
        
        for component in components:
            if hasattr(app, component):
                print(f"   ✅ {component}")
            else:
                print(f"   ❌ {component} missing")
        
        # Test 10: Results content
        print("\n📄 Test 10: Results Content")
        if hasattr(app, 'results_text'):
            content = app.results_text.get(1.0, tk.END)
            lines = content.strip().split('\n')
            print(f"   Results has {len(lines)} lines of content")
            if len(lines) > 5:
                print("✅ Results content populated")
            else:
                print("⚠️ Limited results content")
        
        # Cleanup
        root.destroy()
        
        print("\n" + "=" * 50)
        print("🎉 All results display tests completed successfully!")
        return True
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_visual_components():
    """Test visual components briefly."""
    print("\n🎨 Testing Visual Components")
    print("-" * 30)
    
    try:
        import desktop_app
        import tkinter as tk
        
        root = tk.Tk()
        root.withdraw()
        
        app = desktop_app.VideoSearchGUI(root)
        
        # Test tab switching
        if hasattr(app, 'notebook'):
            print("✅ Notebook (tabs) available")
            
            # Try switching to results tab
            try:
                app.notebook.select(3)  # Results tab
                print("✅ Tab switching works")
            except Exception as e:
                print(f"⚠️ Tab switching issue: {e}")
        
        # Test button states
        if hasattr(app, 'search_button'):
            print("✅ Search button available")
        
        if hasattr(app, 'progress_bar'):
            print("✅ Progress bar available")
        
        root.destroy()
        print("✅ Visual components test completed")
        return True
        
    except Exception as e:
        print(f"❌ Visual components test failed: {e}")
        return False

def main():
    """Run all tests."""
    print("🧪 Desktop App Results Display Test Suite")
    print("=" * 60)
    
    tests_passed = 0
    total_tests = 2
    
    # Test 1: Results display functionality
    if test_results_display():
        tests_passed += 1
    
    # Test 2: Visual components
    if test_visual_components():
        tests_passed += 1
    
    print("\n" + "=" * 60)
    print("📊 TEST SUMMARY")
    print("=" * 60)
    print(f"Tests passed: {tests_passed}/{total_tests}")
    print(f"Success rate: {(tests_passed/total_tests)*100:.1f}%")
    
    if tests_passed == total_tests:
        print("\n🎉 All results display tests passed!")
        print("✅ Results display is working correctly")
        return True
    else:
        print(f"\n⚠️ {total_tests - tests_passed} test(s) failed")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

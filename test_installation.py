#!/usr/bin/env python3
"""
Comprehensive Installation Testing Suite for AI-Powered Video Content Search.
Tests all components, dependencies, and functionality after installation.
"""

import os
import sys
import subprocess
import platform
import time
import json
import tempfile
import shutil
from pathlib import Path
from typing import Dict, List, Tuple, Any, Optional
import logging

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class Colors:
    """ANSI color codes for terminal output."""
    HEADER = '\033[95m'
    OKBLUE = '\033[94m'
    OKCYAN = '\033[96m'
    OKGREEN = '\033[92m'
    WARNING = '\033[93m'
    FAIL = '\033[91m'
    ENDC = '\033[0m'
    BOLD = '\033[1m'


class InstallationTester:
    """Comprehensive installation testing suite."""
    
    def __init__(self):
        self.platform = platform.system()
        self.python_version = sys.version_info
        self.test_results = {}
        self.errors = []
        self.warnings = []
        self.start_time = time.time()
        
    def print_header(self):
        """Print test suite header."""
        print(f"{Colors.HEADER}{Colors.BOLD}")
        print("=" * 80)
        print("🧪 AI-Powered Video Content Search - Installation Test Suite")
        print("=" * 80)
        print(f"{Colors.ENDC}")
        print(f"Platform: {self.platform}")
        print(f"Python: {self.python_version.major}.{self.python_version.minor}.{self.python_version.micro}")
        print(f"Test started at: {time.strftime('%Y-%m-%d %H:%M:%S')}")
        print()
    
    def test_python_environment(self) -> bool:
        """Test Python environment and version."""
        print(f"{Colors.OKBLUE}🐍 Testing Python Environment{Colors.ENDC}")
        
        success = True
        
        # Check Python version
        if self.python_version >= (3, 8):
            print(f"   {Colors.OKGREEN}✅ Python version: {sys.version}{Colors.ENDC}")
        else:
            print(f"   {Colors.FAIL}❌ Python version too old: {sys.version}{Colors.ENDC}")
            self.errors.append("Python 3.8+ required")
            success = False
        
        # Check virtual environment
        in_venv = hasattr(sys, 'real_prefix') or (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix)
        if in_venv:
            print(f"   {Colors.OKGREEN}✅ Running in virtual environment{Colors.ENDC}")
        else:
            print(f"   {Colors.WARNING}⚠️  Not in virtual environment{Colors.ENDC}")
            self.warnings.append("Not using virtual environment")
        
        # Check pip
        try:
            import pip
            print(f"   {Colors.OKGREEN}✅ pip is available{Colors.ENDC}")
        except ImportError:
            print(f"   {Colors.FAIL}❌ pip not available{Colors.ENDC}")
            self.errors.append("pip not available")
            success = False
        
        self.test_results['python_environment'] = success
        return success
    
    def test_core_dependencies(self) -> bool:
        """Test core Python dependencies."""
        print(f"\n{Colors.OKBLUE}📦 Testing Core Dependencies{Colors.ENDC}")
        
        core_packages = [
            ('torch', 'PyTorch'),
            ('torchvision', 'TorchVision'),
            ('transformers', 'Transformers'),
            ('cv2', 'OpenCV'),
            ('streamlit', 'Streamlit'),
            ('PIL', 'Pillow'),
            ('moviepy.editor', 'MoviePy'),
            ('ultralytics', 'Ultralytics'),
            ('numpy', 'NumPy'),
            ('scipy', 'SciPy'),
            ('matplotlib', 'Matplotlib'),
            ('sklearn', 'Scikit-learn'),
            ('pandas', 'Pandas'),
            ('plotly', 'Plotly'),
            ('requests', 'Requests'),
            ('tqdm', 'TQDM'),
            ('psutil', 'PSUtil'),
        ]
        
        failed_imports = []
        
        for module, name in core_packages:
            try:
                __import__(module)
                print(f"   {Colors.OKGREEN}✅ {name}{Colors.ENDC}")
            except ImportError as e:
                print(f"   {Colors.FAIL}❌ {name}: {e}{Colors.ENDC}")
                failed_imports.append(name)
        
        success = len(failed_imports) == 0
        if not success:
            self.errors.extend([f"Missing package: {pkg}" for pkg in failed_imports])
        
        self.test_results['core_dependencies'] = success
        return success
    
    def test_gpu_support(self) -> bool:
        """Test GPU support and acceleration."""
        print(f"\n{Colors.OKBLUE}🎮 Testing GPU Support{Colors.ENDC}")
        
        gpu_available = False
        
        try:
            import torch
            
            # Test CUDA
            if torch.cuda.is_available():
                gpu_count = torch.cuda.device_count()
                gpu_name = torch.cuda.get_device_name(0)
                print(f"   {Colors.OKGREEN}✅ CUDA available: {gpu_name} ({gpu_count} device(s)){Colors.ENDC}")
                gpu_available = True
                
                # Test CUDA memory
                try:
                    device = torch.device('cuda')
                    test_tensor = torch.randn(1000, 1000, device=device)
                    print(f"   {Colors.OKGREEN}✅ CUDA memory test passed{Colors.ENDC}")
                    del test_tensor
                    torch.cuda.empty_cache()
                except Exception as e:
                    print(f"   {Colors.WARNING}⚠️  CUDA memory test failed: {e}{Colors.ENDC}")
                    self.warnings.append("CUDA memory test failed")
            
            # Test MPS (Apple Silicon)
            if hasattr(torch.backends, 'mps') and torch.backends.mps.is_available():
                print(f"   {Colors.OKGREEN}✅ MPS (Apple Silicon) available{Colors.ENDC}")
                gpu_available = True
                
                # Test MPS memory
                try:
                    device = torch.device('mps')
                    test_tensor = torch.randn(1000, 1000, device=device)
                    print(f"   {Colors.OKGREEN}✅ MPS memory test passed{Colors.ENDC}")
                    del test_tensor
                except Exception as e:
                    print(f"   {Colors.WARNING}⚠️  MPS memory test failed: {e}{Colors.ENDC}")
                    self.warnings.append("MPS memory test failed")
            
            if not gpu_available:
                print(f"   {Colors.WARNING}⚠️  No GPU acceleration available (CPU only){Colors.ENDC}")
                self.warnings.append("No GPU acceleration")
        
        except ImportError:
            print(f"   {Colors.FAIL}❌ PyTorch not available for GPU testing{Colors.ENDC}")
            self.errors.append("PyTorch not available")
            return False
        
        self.test_results['gpu_support'] = gpu_available
        return True  # GPU is optional, so return True even if not available
    
    def test_system_dependencies(self) -> bool:
        """Test system-level dependencies."""
        print(f"\n{Colors.OKBLUE}🔧 Testing System Dependencies{Colors.ENDC}")

        system_deps = {
            'git': 'Git version control',
            'ffmpeg': 'FFmpeg multimedia framework'
        }

        missing_deps = []

        for cmd, description in system_deps.items():
            if self._find_system_dependency(cmd):
                print(f"   {Colors.OKGREEN}✅ {cmd} - {description}{Colors.ENDC}")
            else:
                print(f"   {Colors.FAIL}❌ {cmd} - {description}{Colors.ENDC}")
                missing_deps.append(cmd)

        success = len(missing_deps) == 0
        if not success:
            self.errors.extend([f"Missing system dependency: {dep}" for dep in missing_deps])

        self.test_results['system_dependencies'] = success
        return success

    def _find_system_dependency(self, dep_name: str) -> bool:
        """Enhanced dependency detection that checks multiple locations."""
        import shutil

        # First check if it's in PATH
        if shutil.which(dep_name):
            return True

        # Special handling for FFmpeg
        if dep_name == 'ffmpeg':
            # Check local ffmpeg folder
            local_ffmpeg = Path('ffmpeg/ffmpeg.exe')
            if local_ffmpeg.exists():
                return True

            # Check common FFmpeg installation locations
            common_ffmpeg_paths = [
                'C:/ffmpeg/bin/ffmpeg.exe',
                'C:/Program Files/ffmpeg/bin/ffmpeg.exe',
                'C:/ProgramData/chocolatey/lib/ffmpeg/tools/ffmpeg/bin/ffmpeg.exe',
                Path.home() / 'ffmpeg/bin/ffmpeg.exe',
            ]

            for path in common_ffmpeg_paths:
                if Path(path).exists():
                    return True

        return False
    
    def test_file_structure(self) -> bool:
        """Test project file structure."""
        print(f"\n{Colors.OKBLUE}📁 Testing File Structure{Colors.ENDC}")
        
        required_files = [
            'main.py',
            'requirements.txt',
            'config.py',
            'README.md',
        ]
        
        required_dirs = [
            'utils',
            'models',
            'app',
            'static',
            'static/output_clips',
        ]
        
        missing_files = []
        missing_dirs = []
        
        # Check files
        for file_path in required_files:
            if Path(file_path).exists():
                print(f"   {Colors.OKGREEN}✅ {file_path}{Colors.ENDC}")
            else:
                print(f"   {Colors.FAIL}❌ {file_path}{Colors.ENDC}")
                missing_files.append(file_path)
        
        # Check directories
        for dir_path in required_dirs:
            if Path(dir_path).exists():
                print(f"   {Colors.OKGREEN}✅ {dir_path}/{Colors.ENDC}")
            else:
                print(f"   {Colors.FAIL}❌ {dir_path}/{Colors.ENDC}")
                missing_dirs.append(dir_path)
        
        success = len(missing_files) == 0 and len(missing_dirs) == 0
        if not success:
            self.errors.extend([f"Missing file: {f}" for f in missing_files])
            self.errors.extend([f"Missing directory: {d}" for d in missing_dirs])
        
        self.test_results['file_structure'] = success
        return success
    
    def test_core_functionality(self) -> bool:
        """Test core application functionality."""
        print(f"\n{Colors.OKBLUE}⚙️  Testing Core Functionality{Colors.ENDC}")
        
        success = True
        
        # Test CLIP model loading
        try:
            from models.clip_model import CLIPMatcher
            clip_matcher = CLIPMatcher()
            print(f"   {Colors.OKGREEN}✅ CLIP model loading{Colors.ENDC}")
        except Exception as e:
            print(f"   {Colors.FAIL}❌ CLIP model loading: {e}{Colors.ENDC}")
            self.errors.append(f"CLIP model loading failed: {e}")
            success = False
        
        # Test frame extraction
        try:
            from utils.frame_extraction import FrameExtractor
            extractor = FrameExtractor()
            print(f"   {Colors.OKGREEN}✅ Frame extraction module{Colors.ENDC}")
        except Exception as e:
            print(f"   {Colors.FAIL}❌ Frame extraction module: {e}{Colors.ENDC}")
            self.errors.append(f"Frame extraction failed: {e}")
            success = False
        
        # Test video search engine
        try:
            from utils.clip_match import VideoSearchEngine
            search_engine = VideoSearchEngine()
            print(f"   {Colors.OKGREEN}✅ Video search engine{Colors.ENDC}")
        except Exception as e:
            print(f"   {Colors.FAIL}❌ Video search engine: {e}{Colors.ENDC}")
            self.errors.append(f"Video search engine failed: {e}")
            success = False
        
        # Test web interface
        try:
            from app.interface import main as web_main
            print(f"   {Colors.OKGREEN}✅ Web interface module{Colors.ENDC}")
        except Exception as e:
            print(f"   {Colors.FAIL}❌ Web interface module: {e}{Colors.ENDC}")
            self.errors.append(f"Web interface failed: {e}")
            success = False
        
        self.test_results['core_functionality'] = success
        return success
    
    def test_configuration(self) -> bool:
        """Test configuration system."""
        print(f"\n{Colors.OKBLUE}⚙️  Testing Configuration{Colors.ENDC}")
        
        success = True
        
        # Test basic config
        try:
            import config
            print(f"   {Colors.OKGREEN}✅ Basic configuration{Colors.ENDC}")
        except Exception as e:
            print(f"   {Colors.FAIL}❌ Basic configuration: {e}{Colors.ENDC}")
            self.errors.append(f"Basic configuration failed: {e}")
            success = False
        
        # Test advanced config
        try:
            from config_advanced import AdvancedConfig
            advanced_config = AdvancedConfig()
            print(f"   {Colors.OKGREEN}✅ Advanced configuration{Colors.ENDC}")
        except Exception as e:
            print(f"   {Colors.FAIL}❌ Advanced configuration: {e}{Colors.ENDC}")
            self.errors.append(f"Advanced configuration failed: {e}")
            success = False
        
        self.test_results['configuration'] = success
        return success
    
    def test_memory_usage(self) -> bool:
        """Test memory usage and performance."""
        print(f"\n{Colors.OKBLUE}💾 Testing Memory Usage{Colors.ENDC}")
        
        try:
            import psutil
            
            # Get current memory usage
            memory = psutil.virtual_memory()
            memory_gb = memory.total / (1024**3)
            memory_used_percent = memory.percent
            
            print(f"   {Colors.OKGREEN}✅ Total memory: {memory_gb:.1f} GB{Colors.ENDC}")
            print(f"   {Colors.OKGREEN}✅ Memory usage: {memory_used_percent:.1f}%{Colors.ENDC}")
            
            if memory_gb < 4:
                print(f"   {Colors.WARNING}⚠️  Low memory: {memory_gb:.1f} GB (4GB+ recommended){Colors.ENDC}")
                self.warnings.append("Low system memory")
            
            if memory_used_percent > 80:
                print(f"   {Colors.WARNING}⚠️  High memory usage: {memory_used_percent:.1f}%{Colors.ENDC}")
                self.warnings.append("High memory usage")
            
            self.test_results['memory_usage'] = True
            return True
            
        except Exception as e:
            print(f"   {Colors.FAIL}❌ Memory testing failed: {e}{Colors.ENDC}")
            self.errors.append(f"Memory testing failed: {e}")
            self.test_results['memory_usage'] = False
            return False
    
    def test_sample_functionality(self) -> bool:
        """Test with a sample video if available."""
        print(f"\n{Colors.OKBLUE}🎬 Testing Sample Functionality{Colors.ENDC}")
        
        # Look for test videos
        test_video_paths = [
            "temp_videos/sample.mp4",
            "test_data/sample.mp4",
            "videos/sample.mp4"
        ]
        
        test_video = None
        for path in test_video_paths:
            if Path(path).exists():
                test_video = path
                break
        
        if not test_video:
            print(f"   {Colors.WARNING}⚠️  No test video found, skipping functional test{Colors.ENDC}")
            self.warnings.append("No test video available")
            self.test_results['sample_functionality'] = True
            return True
        
        try:
            from utils.clip_match import VideoSearchEngine
            
            print(f"   {Colors.OKGREEN}✅ Found test video: {test_video}{Colors.ENDC}")
            
            # Test basic search
            search_engine = VideoSearchEngine()
            results = search_engine.search_video(
                video_path=test_video,
                query="test",
                similarity_threshold=0.1,
                top_k=5,
                create_clips=False,
                create_thumbnails=True
            )
            
            if results['success']:
                print(f"   {Colors.OKGREEN}✅ Sample search completed successfully{Colors.ENDC}")
                print(f"   {Colors.OKGREEN}✅ Found {len(results['matches'])} matches{Colors.ENDC}")
                print(f"   {Colors.OKGREEN}✅ Processing time: {results['processing_time']:.2f}s{Colors.ENDC}")
            else:
                print(f"   {Colors.FAIL}❌ Sample search failed: {results.get('error', 'Unknown error')}{Colors.ENDC}")
                self.errors.append("Sample search failed")
                self.test_results['sample_functionality'] = False
                return False
            
            self.test_results['sample_functionality'] = True
            return True
            
        except Exception as e:
            print(f"   {Colors.FAIL}❌ Sample functionality test failed: {e}{Colors.ENDC}")
            self.errors.append(f"Sample functionality test failed: {e}")
            self.test_results['sample_functionality'] = False
            return False
    
    def generate_test_report(self) -> Dict[str, Any]:
        """Generate comprehensive test report."""
        end_time = time.time()
        test_duration = end_time - self.start_time
        
        # Calculate success rate
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results.values() if result)
        success_rate = (passed_tests / total_tests * 100) if total_tests > 0 else 0
        
        report = {
            'timestamp': time.strftime('%Y-%m-%d %H:%M:%S'),
            'platform': self.platform,
            'python_version': f"{self.python_version.major}.{self.python_version.minor}.{self.python_version.micro}",
            'test_duration': test_duration,
            'total_tests': total_tests,
            'passed_tests': passed_tests,
            'success_rate': success_rate,
            'test_results': self.test_results,
            'errors': self.errors,
            'warnings': self.warnings
        }
        
        return report
    
    def print_summary(self, report: Dict[str, Any]):
        """Print test summary."""
        print(f"\n{Colors.HEADER}{Colors.BOLD}")
        print("=" * 80)
        print("📋 Test Summary")
        print("=" * 80)
        print(f"{Colors.ENDC}")
        
        # Overall status
        if report['success_rate'] == 100:
            print(f"{Colors.OKGREEN}{Colors.BOLD}🎉 All tests passed! Installation is successful.{Colors.ENDC}")
        elif report['success_rate'] >= 80:
            print(f"{Colors.WARNING}{Colors.BOLD}⚠️  Most tests passed with some warnings.{Colors.ENDC}")
        else:
            print(f"{Colors.FAIL}{Colors.BOLD}❌ Multiple tests failed. Installation needs attention.{Colors.ENDC}")
        
        print(f"\n📊 Test Statistics:")
        print(f"   Total tests: {report['total_tests']}")
        print(f"   Passed: {report['passed_tests']}")
        print(f"   Success rate: {report['success_rate']:.1f}%")
        print(f"   Duration: {report['test_duration']:.2f} seconds")
        
        # Test results breakdown
        print(f"\n📋 Test Results:")
        for test_name, result in report['test_results'].items():
            status = f"{Colors.OKGREEN}✅ PASS" if result else f"{Colors.FAIL}❌ FAIL"
            print(f"   {status} {test_name.replace('_', ' ').title()}{Colors.ENDC}")
        
        # Warnings
        if report['warnings']:
            print(f"\n{Colors.WARNING}⚠️  Warnings ({len(report['warnings'])}):${Colors.ENDC}")
            for warning in report['warnings']:
                print(f"   - {warning}")
        
        # Errors
        if report['errors']:
            print(f"\n{Colors.FAIL}❌ Errors ({len(report['errors'])}):${Colors.ENDC}")
            for error in report['errors']:
                print(f"   - {error}")
        
        # Recommendations
        print(f"\n{Colors.OKBLUE}💡 Recommendations:{Colors.ENDC}")
        if report['success_rate'] == 100:
            print("   🎯 Installation is complete and ready for use!")
            print("   🚀 Start the web interface: python main.py --web")
        else:
            print("   🔧 Review and fix the errors listed above")
            print("   📖 Check the documentation for troubleshooting")
            print("   🔄 Re-run the installation if needed")
        
        print(f"\n{Colors.HEADER}=" * 80 + f"{Colors.ENDC}")
    
    def save_report(self, report: Dict[str, Any], filename: str = "test_report.json"):
        """Save test report to file."""
        try:
            with open(filename, 'w') as f:
                json.dump(report, f, indent=2)
            print(f"\n📄 Test report saved to: {filename}")
        except Exception as e:
            print(f"\n{Colors.WARNING}⚠️  Failed to save test report: {e}{Colors.ENDC}")
    
    def run_all_tests(self) -> bool:
        """Run all installation tests."""
        self.print_header()
        
        # Run all test categories
        test_functions = [
            self.test_python_environment,
            self.test_core_dependencies,
            self.test_gpu_support,
            self.test_system_dependencies,
            self.test_file_structure,
            self.test_core_functionality,
            self.test_configuration,
            self.test_memory_usage,
            self.test_sample_functionality,
        ]
        
        for test_func in test_functions:
            try:
                test_func()
            except Exception as e:
                print(f"{Colors.FAIL}❌ Test {test_func.__name__} crashed: {e}{Colors.ENDC}")
                self.errors.append(f"Test {test_func.__name__} crashed: {e}")
                self.test_results[test_func.__name__] = False
        
        # Generate and display report
        report = self.generate_test_report()
        self.print_summary(report)
        self.save_report(report)
        
        return report['success_rate'] >= 80


def main():
    """Main testing function."""
    tester = InstallationTester()
    
    try:
        success = tester.run_all_tests()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print(f"\n{Colors.WARNING}⚠️  Testing interrupted by user{Colors.ENDC}")
        sys.exit(1)
    except Exception as e:
        print(f"\n{Colors.FAIL}❌ Unexpected error during testing: {e}{Colors.ENDC}")
        logger.exception("Testing failed with unexpected error")
        sys.exit(1)


if __name__ == "__main__":
    main()

#!/usr/bin/env python3
"""
Batch video search script for processing multiple videos.
"""

import os
import sys
import subprocess
from pathlib import Path

def batch_search(video_directory, query, output_base_dir="batch_results"):
    """
    Search for content across multiple videos in a directory.
    
    Args:
        video_directory: Directory containing video files
        query: Search query
        output_base_dir: Base directory for results
    """
    video_dir = Path(video_directory)
    if not video_dir.exists():
        print(f"❌ Directory not found: {video_directory}")
        return
    
    # Supported video formats
    video_extensions = ['.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv', '.webm']
    
    # Find all video files
    video_files = []
    for ext in video_extensions:
        video_files.extend(video_dir.glob(f"*{ext}"))
        video_files.extend(video_dir.glob(f"*{ext.upper()}"))
    
    if not video_files:
        print(f"❌ No video files found in {video_directory}")
        return
    
    print(f"🎬 Found {len(video_files)} video files")
    print(f"🔍 Searching for: '{query}'")
    print("-" * 50)
    
    results_summary = []
    
    for i, video_file in enumerate(video_files, 1):
        print(f"\n📹 Processing {i}/{len(video_files)}: {video_file.name}")
        
        # Create output directory for this video
        video_output_dir = Path(output_base_dir) / video_file.stem
        video_output_dir.mkdir(parents=True, exist_ok=True)
        
        # Run search command
        cmd = [
            sys.executable, "main.py",
            "--video", str(video_file),
            "--query", query,
            "--thumbnails",
            "--clips",
            "--output-dir", str(video_output_dir),
            "--threshold", "0.2",
            "--max-results", "10"
        ]
        
        try:
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
            
            if result.returncode == 0:
                print(f"   ✅ Search completed successfully")
                # Count results (simple approach - count thumbnail files)
                thumbnail_count = len(list(video_output_dir.glob("*.jpg")))
                results_summary.append((video_file.name, thumbnail_count, "Success"))
            else:
                print(f"   ❌ Search failed: {result.stderr}")
                results_summary.append((video_file.name, 0, "Failed"))
                
        except subprocess.TimeoutExpired:
            print(f"   ⏰ Search timed out (>5 minutes)")
            results_summary.append((video_file.name, 0, "Timeout"))
        except Exception as e:
            print(f"   ❌ Error: {e}")
            results_summary.append((video_file.name, 0, "Error"))
    
    # Print summary
    print("\n" + "=" * 60)
    print("📊 BATCH SEARCH SUMMARY")
    print("=" * 60)
    print(f"Query: '{query}'")
    print(f"Videos processed: {len(video_files)}")
    print(f"Results saved to: {output_base_dir}")
    print("-" * 60)
    
    total_matches = 0
    for video_name, match_count, status in results_summary:
        status_icon = "✅" if status == "Success" else "❌"
        print(f"{status_icon} {video_name:<30} {match_count:>3} matches  ({status})")
        if status == "Success":
            total_matches += match_count
    
    print("-" * 60)
    print(f"🎯 Total matches found: {total_matches}")
    print(f"📁 Results location: {Path(output_base_dir).absolute()}")

if __name__ == "__main__":
    if len(sys.argv) < 3:
        print("Usage: python batch_search.py <video_directory> <query> [output_directory]")
        print("\nExample:")
        print("  python batch_search.py videos/ \"red car\" my_results")
        sys.exit(1)
    
    video_dir = sys.argv[1]
    query = sys.argv[2]
    output_dir = sys.argv[3] if len(sys.argv) > 3 else "batch_results"
    
    batch_search(video_dir, query, output_dir)

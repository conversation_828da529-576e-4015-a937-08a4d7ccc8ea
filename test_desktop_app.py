#!/usr/bin/env python3
"""
Test script for the AI Video Search Desktop Application.
Verifies all components work correctly before launching.
"""

import sys
import os
import traceback

def test_imports():
    """Test all required imports."""
    print("🔍 Testing imports...")
    
    try:
        import tkinter as tk
        print("   ✅ tkinter")
    except ImportError:
        print("   ❌ tkinter - Install with: sudo apt-get install python3-tk (Linux)")
        return False
    
    try:
        from PIL import Image, ImageTk
        print("   ✅ PIL (Pillow)")
    except ImportError:
        print("   ❌ PIL - Install with: pip install Pillow")
        return False
    
    try:
        import cv2
        print("   ✅ OpenCV")
    except ImportError:
        print("   ❌ OpenCV - Install with: pip install opencv-python")
        return False
    
    try:
        import torch
        print("   ✅ PyTorch")
    except ImportError:
        print("   ❌ PyTorch - Install with: pip install torch")
        return False
    
    try:
        import transformers
        print("   ✅ Transformers")
    except ImportError:
        print("   ❌ Transformers - Install with: pip install transformers")
        return False
    
    return True

def test_project_imports():
    """Test project-specific imports."""
    print("\n🔍 Testing project imports...")
    
    try:
        from config_advanced import AdvancedConfig
        print("   ✅ AdvancedConfig")
    except ImportError as e:
        print(f"   ❌ AdvancedConfig - {e}")
        return False
    
    try:
        from utils.clip_match import VideoSearchEngine
        print("   ✅ VideoSearchEngine")
    except ImportError as e:
        print(f"   ❌ VideoSearchEngine - {e}")
        return False
    
    try:
        from utils.live_detection import LiveVideoDetector
        print("   ✅ LiveVideoDetector")
    except ImportError as e:
        print(f"   ❌ LiveVideoDetector - {e}")
        return False
    
    try:
        import desktop_app
        print("   ✅ desktop_app")
    except ImportError as e:
        print(f"   ❌ desktop_app - {e}")
        return False
    
    return True

def test_configuration():
    """Test configuration loading."""
    print("\n⚙️ Testing configuration...")
    
    try:
        from config_advanced import AdvancedConfig
        config = AdvancedConfig()
        
        # Test all config sections
        assert hasattr(config, 'models'), "Missing models config"
        assert hasattr(config, 'processing'), "Missing processing config"
        assert hasattr(config, 'search'), "Missing search config"
        assert hasattr(config, 'output'), "Missing output config"
        assert hasattr(config, 'memory'), "Missing memory config"
        assert hasattr(config, 'performance'), "Missing performance config"
        assert hasattr(config, 'live_detection'), "Missing live_detection config"
        assert hasattr(config, 'web_interface'), "Missing web_interface config"
        assert hasattr(config, 'logging'), "Missing logging config"
        
        print("   ✅ All configuration sections present")
        
        # Test performance config specifically
        assert hasattr(config.performance, 'enable_gpu'), "Missing enable_gpu"
        assert hasattr(config.performance, 'batch_size'), "Missing batch_size"
        assert hasattr(config.performance, 'num_workers'), "Missing num_workers"
        
        print("   ✅ Performance configuration working")
        return True
        
    except Exception as e:
        print(f"   ❌ Configuration error: {e}")
        return False

def test_gui_creation():
    """Test GUI creation without showing window."""
    print("\n🖥️ Testing GUI creation...")
    
    try:
        import tkinter as tk
        from desktop_app import VideoSearchGUI
        
        # Create hidden root window
        root = tk.Tk()
        root.withdraw()  # Hide window
        
        # Create app
        app = VideoSearchGUI(root)
        
        if app.config is None:
            print("   ❌ App configuration failed")
            return False
        
        # Test that key components exist
        assert hasattr(app, 'notebook'), "Missing notebook"
        assert hasattr(app, 'search_engine'), "Missing search_engine"
        assert hasattr(app, 'video_path_var'), "Missing video_path_var"
        assert hasattr(app, 'query_var'), "Missing query_var"
        
        print("   ✅ GUI components created successfully")
        
        # Clean up
        root.destroy()
        return True
        
    except Exception as e:
        print(f"   ❌ GUI creation error: {e}")
        traceback.print_exc()
        return False

def test_camera_access():
    """Test camera access."""
    print("\n📹 Testing camera access...")
    
    try:
        import cv2
        
        # Try to access default camera
        cap = cv2.VideoCapture(0)
        
        if cap.isOpened():
            print("   ✅ Camera 0 accessible")
            cap.release()
            return True
        else:
            print("   ⚠️ Camera 0 not accessible (this is OK if no camera)")
            return True  # Not a failure, just no camera
            
    except Exception as e:
        print(f"   ⚠️ Camera test error: {e} (this is OK if no camera)")
        return True  # Not a critical failure

def test_video_file_support():
    """Test video file format support."""
    print("\n🎬 Testing video file support...")
    
    try:
        import cv2
        
        # Test if OpenCV can handle common formats
        supported_formats = ['.mp4', '.avi', '.mov', '.mkv']
        print(f"   ✅ OpenCV supports formats: {', '.join(supported_formats)}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Video support error: {e}")
        return False

def main():
    """Run all tests."""
    print("🧪 AI Video Search Desktop App - Component Test")
    print("=" * 60)
    
    tests = [
        ("Basic Imports", test_imports),
        ("Project Imports", test_project_imports),
        ("Configuration", test_configuration),
        ("GUI Creation", test_gui_creation),
        ("Camera Access", test_camera_access),
        ("Video Support", test_video_file_support),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}")
        print("-" * 30)
        
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} PASSED")
            else:
                print(f"❌ {test_name} FAILED")
        except Exception as e:
            print(f"❌ {test_name} ERROR: {e}")
    
    print("\n" + "=" * 60)
    print("📊 TEST SUMMARY")
    print("=" * 60)
    print(f"Passed: {passed}/{total}")
    print(f"Success Rate: {(passed/total)*100:.1f}%")
    
    if passed == total:
        print("\n🎉 All tests passed! Desktop app is ready to use.")
        print("🚀 Run: python desktop_app.py")
        return True
    else:
        print(f"\n⚠️ {total-passed} test(s) failed. Check the errors above.")
        print("💡 Install missing dependencies and try again.")
        return False

if __name__ == "__main__":
    success = main()
    
    if not success:
        input("\nPress Enter to exit...")
    
    sys.exit(0 if success else 1)

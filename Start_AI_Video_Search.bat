@echo off
title AI Video Search Desktop Application
color 0A

echo.
echo ========================================
echo   AI Video Search Desktop Application
echo ========================================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python is not installed or not in PATH
    echo Please install Python 3.8 or higher
    pause
    exit /b 1
)

REM Check if desktop_app.py exists
if not exist "desktop_app.py" (
    echo ERROR: desktop_app.py not found!
    echo Make sure you're running this from the correct directory
    pause
    exit /b 1
)

echo Starting desktop application...
echo.

REM Run the desktop application
python desktop_app.py

if errorlevel 1 (
    echo.
    echo ERROR: Application failed to start
    echo Check the error messages above
    pause
)

echo.
echo Application closed.
pause

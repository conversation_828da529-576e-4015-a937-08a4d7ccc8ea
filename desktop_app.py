#!/usr/bin/env python3
"""
AI-Powered Video Content Search - Desktop GUI Application
A standalone desktop application with full GUI interface.
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import threading
import os
import sys
from pathlib import Path
from PIL import Image, ImageTk
import cv2
import time
import queue
import traceback

# Add project root to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from utils.clip_match import VideoSearchEngine
    from utils.live_detection import LiveVideoDetector
    from config_advanced import AdvancedConfig
except ImportError as e:
    print(f"❌ Import error: {e}")
    print("💡 Make sure you're running from the correct directory")
    sys.exit(1)

class VideoSearchGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("AI-Powered Video Content Search")
        self.root.geometry("1200x800")
        self.root.configure(bg='#f0f0f0')

        # Initialize configuration
        try:
            self.config = AdvancedConfig()
        except Exception as e:
            messagebox.showerror("Configuration Error", f"Failed to load configuration: {e}")
            self.config = None
            return

        self.search_engine = None
        self.live_detector = None
        self.current_video_path = None
        self.search_results = []
        self.is_searching = False
        self.is_live_detecting = False
        self.live_detection_thread = None

        # Create GUI
        try:
            self.create_widgets()
            self.setup_styles()
        except Exception as e:
            messagebox.showerror("GUI Error", f"Failed to create interface: {e}")
            return

        # Initialize search engine
        self.initialize_search_engine()
    
    def setup_styles(self):
        """Setup custom styles for the GUI."""
        style = ttk.Style()
        style.theme_use('clam')
        
        # Configure custom styles
        style.configure('Title.TLabel', font=('Arial', 16, 'bold'), background='#f0f0f0')
        style.configure('Header.TLabel', font=('Arial', 12, 'bold'), background='#f0f0f0')
        style.configure('Success.TLabel', foreground='green', background='#f0f0f0')
        style.configure('Error.TLabel', foreground='red', background='#f0f0f0')
    
    def create_widgets(self):
        """Create all GUI widgets."""
        # Main title
        title_label = ttk.Label(self.root, text="🎬 AI-Powered Video Content Search", style='Title.TLabel')
        title_label.pack(pady=10)
        
        # Create notebook for tabs
        self.notebook = ttk.Notebook(self.root)
        self.notebook.pack(fill='both', expand=True, padx=10, pady=5)
        
        # Create tabs
        self.create_video_search_tab()
        self.create_live_detection_tab()
        self.create_settings_tab()
        self.create_results_tab()
    
    def create_video_search_tab(self):
        """Create video search tab."""
        # Video Search Tab
        video_frame = ttk.Frame(self.notebook)
        self.notebook.add(video_frame, text="📁 Video Search")
        
        # Video file selection
        file_frame = ttk.LabelFrame(video_frame, text="Video File", padding=10)
        file_frame.pack(fill='x', padx=10, pady=5)
        
        self.video_path_var = tk.StringVar()
        ttk.Label(file_frame, text="Select Video File:").pack(anchor='w')
        
        file_select_frame = ttk.Frame(file_frame)
        file_select_frame.pack(fill='x', pady=5)
        
        self.video_path_entry = ttk.Entry(file_select_frame, textvariable=self.video_path_var, width=60)
        self.video_path_entry.pack(side='left', fill='x', expand=True)
        
        ttk.Button(file_select_frame, text="Browse", command=self.browse_video_file).pack(side='right', padx=(5,0))
        
        # Search parameters
        params_frame = ttk.LabelFrame(video_frame, text="Search Parameters", padding=10)
        params_frame.pack(fill='x', padx=10, pady=5)
        
        # Query input
        ttk.Label(params_frame, text="Search Query:").pack(anchor='w')
        self.query_var = tk.StringVar()
        query_entry = ttk.Entry(params_frame, textvariable=self.query_var, width=50)
        query_entry.pack(fill='x', pady=2)
        
        # Parameters in grid
        params_grid = ttk.Frame(params_frame)
        params_grid.pack(fill='x', pady=5)
        
        # Similarity threshold
        ttk.Label(params_grid, text="Similarity Threshold:").grid(row=0, column=0, sticky='w', padx=(0,10))
        self.threshold_var = tk.DoubleVar(value=0.2)
        threshold_scale = ttk.Scale(params_grid, from_=0.1, to=0.5, variable=self.threshold_var, orient='horizontal')
        threshold_scale.grid(row=0, column=1, sticky='ew', padx=(0,10))
        self.threshold_label = ttk.Label(params_grid, text="0.2")
        self.threshold_label.grid(row=0, column=2)
        threshold_scale.configure(command=self.update_threshold_label)
        
        # Max results
        ttk.Label(params_grid, text="Max Results:").grid(row=1, column=0, sticky='w', padx=(0,10), pady=(5,0))
        self.max_results_var = tk.IntVar(value=20)
        max_results_spin = ttk.Spinbox(params_grid, from_=1, to=100, textvariable=self.max_results_var, width=10)
        max_results_spin.grid(row=1, column=1, sticky='w', pady=(5,0))
        
        params_grid.columnconfigure(1, weight=1)
        
        # Options
        options_frame = ttk.Frame(params_frame)
        options_frame.pack(fill='x', pady=5)
        
        self.create_clips_var = tk.BooleanVar(value=True)
        self.create_thumbnails_var = tk.BooleanVar(value=True)
        
        ttk.Checkbutton(options_frame, text="Create Video Clips", variable=self.create_clips_var).pack(side='left')
        ttk.Checkbutton(options_frame, text="Create Thumbnails", variable=self.create_thumbnails_var).pack(side='left', padx=(20,0))
        
        # Search button
        search_button_frame = ttk.Frame(video_frame)
        search_button_frame.pack(fill='x', padx=10, pady=10)
        
        self.search_button = ttk.Button(search_button_frame, text="🔍 Start Search", command=self.start_video_search)
        self.search_button.pack(side='left')
        
        self.stop_search_button = ttk.Button(search_button_frame, text="⏹️ Stop Search", command=self.stop_video_search, state='disabled')
        self.stop_search_button.pack(side='left', padx=(10,0))
        
        # Progress bar
        self.progress_var = tk.StringVar(value="Ready")
        ttk.Label(search_button_frame, textvariable=self.progress_var).pack(side='right')
        
        self.progress_bar = ttk.Progressbar(video_frame, mode='indeterminate')
        self.progress_bar.pack(fill='x', padx=10, pady=5)
    
    def create_live_detection_tab(self):
        """Create live detection tab."""
        live_frame = ttk.Frame(self.notebook)
        self.notebook.add(live_frame, text="📹 Live Detection")
        
        # Live detection controls
        controls_frame = ttk.LabelFrame(live_frame, text="Live Detection Controls", padding=10)
        controls_frame.pack(fill='x', padx=10, pady=5)
        
        # Query for live detection
        ttk.Label(controls_frame, text="Search Query:").pack(anchor='w')
        self.live_query_var = tk.StringVar()
        ttk.Entry(controls_frame, textvariable=self.live_query_var, width=50).pack(fill='x', pady=2)
        
        # Live detection parameters
        live_params = ttk.Frame(controls_frame)
        live_params.pack(fill='x', pady=5)
        
        ttk.Label(live_params, text="Detection Interval (seconds):").pack(side='left')
        self.live_interval_var = tk.DoubleVar(value=0.5)
        ttk.Spinbox(live_params, from_=0.1, to=2.0, increment=0.1, textvariable=self.live_interval_var, width=10).pack(side='left', padx=(5,20))
        
        ttk.Label(live_params, text="Camera:").pack(side='left')
        self.camera_var = tk.IntVar(value=0)
        ttk.Spinbox(live_params, from_=0, to=3, textvariable=self.camera_var, width=5).pack(side='left', padx=(5,0))
        
        # Live detection buttons
        live_buttons = ttk.Frame(controls_frame)
        live_buttons.pack(fill='x', pady=10)
        
        self.start_live_button = ttk.Button(live_buttons, text="▶️ Start Live Detection", command=self.start_live_detection)
        self.start_live_button.pack(side='left')
        
        self.pause_live_button = ttk.Button(live_buttons, text="⏸️ Pause", command=self.pause_live_detection, state='disabled')
        self.pause_live_button.pack(side='left', padx=(10,0))
        
        self.stop_live_button = ttk.Button(live_buttons, text="⏹️ Stop", command=self.stop_live_detection, state='disabled')
        self.stop_live_button.pack(side='left', padx=(10,0))
        
        # Live detection status
        self.live_status_var = tk.StringVar(value="Ready")
        ttk.Label(live_buttons, textvariable=self.live_status_var, style='Header.TLabel').pack(side='right')
        
        # Live results frame
        live_results_frame = ttk.LabelFrame(live_frame, text="Live Detection Results", padding=10)
        live_results_frame.pack(fill='both', expand=True, padx=10, pady=5)
        
        # Results display
        self.live_results_text = scrolledtext.ScrolledText(live_results_frame, height=15, width=80)
        self.live_results_text.pack(fill='both', expand=True)
    
    def create_settings_tab(self):
        """Create settings tab."""
        settings_frame = ttk.Frame(self.notebook)
        self.notebook.add(settings_frame, text="⚙️ Settings")
        
        # Model settings
        model_frame = ttk.LabelFrame(settings_frame, text="Model Settings", padding=10)
        model_frame.pack(fill='x', padx=10, pady=5)
        
        ttk.Label(model_frame, text="CLIP Model:").pack(anchor='w')
        self.clip_model_var = tk.StringVar(value=self.config.models.clip_model_name)
        clip_combo = ttk.Combobox(model_frame, textvariable=self.clip_model_var, values=[
            "openai/clip-vit-base-patch32",
            "openai/clip-vit-large-patch14",
            "openai/clip-vit-base-patch16"
        ])
        clip_combo.pack(fill='x', pady=2)
        
        # Performance settings
        perf_frame = ttk.LabelFrame(settings_frame, text="Performance Settings", padding=10)
        perf_frame.pack(fill='x', padx=10, pady=5)
        
        self.enable_gpu_var = tk.BooleanVar(value=self.config.performance.enable_gpu)
        ttk.Checkbutton(perf_frame, text="Enable GPU Acceleration", variable=self.enable_gpu_var).pack(anchor='w')
        
        self.parallel_processing_var = tk.BooleanVar(value=self.config.performance.enable_parallel_processing)
        ttk.Checkbutton(perf_frame, text="Enable Parallel Processing", variable=self.parallel_processing_var).pack(anchor='w')
        
        # Frame extraction settings
        frame_frame = ttk.LabelFrame(settings_frame, text="Frame Extraction Settings", padding=10)
        frame_frame.pack(fill='x', padx=10, pady=5)
        
        ttk.Label(frame_frame, text="Frame Interval:").pack(anchor='w')
        self.frame_interval_var = tk.IntVar(value=self.config.processing.default_frame_interval)
        ttk.Spinbox(frame_frame, from_=5, to=120, textvariable=self.frame_interval_var, width=10).pack(anchor='w', pady=2)
        
        # Save settings button
        ttk.Button(settings_frame, text="💾 Save Settings", command=self.save_settings).pack(pady=20)
    
    def create_results_tab(self):
        """Create results tab."""
        results_frame = ttk.Frame(self.notebook)
        self.notebook.add(results_frame, text="📊 Results")
        
        # Results display
        self.results_text = scrolledtext.ScrolledText(results_frame, height=25, width=100)
        self.results_text.pack(fill='both', expand=True, padx=10, pady=10)
        
        # Results buttons
        results_buttons = ttk.Frame(results_frame)
        results_buttons.pack(fill='x', padx=10, pady=5)
        
        ttk.Button(results_buttons, text="📁 Open Results Folder", command=self.open_results_folder).pack(side='left')
        ttk.Button(results_buttons, text="🗑️ Clear Results", command=self.clear_results).pack(side='left', padx=(10,0))
        ttk.Button(results_buttons, text="💾 Export Results", command=self.export_results).pack(side='left', padx=(10,0))

    def initialize_search_engine(self):
        """Initialize the search engine."""
        try:
            self.search_engine = VideoSearchEngine(
                clip_model_name=self.config.models.clip_model_name,
                frame_interval=self.config.processing.default_frame_interval,
                enable_parallel_processing=self.config.performance.enable_parallel_processing,
                max_workers=self.config.performance.max_workers
            )
            self.log_message("✅ Search engine initialized successfully")
        except Exception as e:
            self.log_message(f"❌ Failed to initialize search engine: {e}")

    def browse_video_file(self):
        """Browse for video file."""
        filetypes = [
            ("Video files", "*.mp4 *.avi *.mov *.mkv *.wmv *.flv *.webm"),
            ("MP4 files", "*.mp4"),
            ("AVI files", "*.avi"),
            ("All files", "*.*")
        ]

        try:
            filename = filedialog.askopenfilename(
                title="Select Video File",
                filetypes=filetypes
            )

            if filename:
                # Validate file exists and is readable
                if not os.path.exists(filename):
                    messagebox.showerror("Error", "Selected file does not exist!")
                    return

                if not os.access(filename, os.R_OK):
                    messagebox.showerror("Error", "Cannot read the selected file!")
                    return

                self.video_path_var.set(filename)
                self.current_video_path = filename
                self.log_message(f"📁 Selected video: {os.path.basename(filename)}")

        except Exception as e:
            messagebox.showerror("Error", f"Failed to select video file: {e}")

    def update_threshold_label(self, value):
        """Update threshold label."""
        self.threshold_label.config(text=f"{float(value):.2f}")

    def start_video_search(self):
        """Start video search in a separate thread."""
        if not self.video_path_var.get():
            messagebox.showerror("Error", "Please select a video file first!")
            return

        if not self.query_var.get().strip():
            messagebox.showerror("Error", "Please enter a search query!")
            return

        if self.is_searching:
            messagebox.showwarning("Warning", "Search is already in progress!")
            return

        # Update UI
        self.is_searching = True
        self.search_button.config(state='disabled')
        self.stop_search_button.config(state='normal')
        self.progress_bar.start()
        self.progress_var.set("Searching...")

        # Start search in separate thread
        search_thread = threading.Thread(target=self.perform_video_search, daemon=True)
        search_thread.start()

    def perform_video_search(self):
        """Perform the actual video search."""
        try:
            video_path = self.video_path_var.get()
            query = self.query_var.get().strip()
            threshold = self.threshold_var.get()
            max_results = self.max_results_var.get()
            create_clips = self.create_clips_var.get()
            create_thumbnails = self.create_thumbnails_var.get()

            self.log_message(f"🔍 Starting search for '{query}' in {os.path.basename(video_path)}")
            self.log_message(f"📊 Parameters: threshold={threshold}, max_results={max_results}")

            # Perform search
            results = self.search_engine.search_video(
                video_path=video_path,
                query=query,
                similarity_threshold=threshold,
                top_k=max_results,
                create_clips=create_clips,
                create_thumbnails=create_thumbnails,
                use_advanced_matching=True
            )

            # Process results
            self.root.after(0, self.handle_search_results, results)

        except Exception as e:
            error_msg = f"❌ Search failed: {str(e)}"
            self.root.after(0, self.log_message, error_msg)
            self.root.after(0, self.search_completed)

    def handle_search_results(self, results):
        """Handle search results on main thread."""
        try:
            if results['success']:
                matches = results['matches']
                processing_time = results['processing_time']

                self.log_message(f"✅ Search completed successfully!")
                self.log_message(f"🎯 Found {len(matches)} matches in {processing_time:.2f} seconds")

                if matches:
                    self.log_message("\n📋 Match Details:")
                    for i, match in enumerate(matches, 1):
                        timestamp = match['timestamp']
                        score = match['similarity_score']
                        frame_idx = match['frame_index']

                        minutes = int(timestamp // 60)
                        seconds = int(timestamp % 60)

                        self.log_message(f"  {i:2d}. Frame {frame_idx:4d} at {minutes:02d}:{seconds:02d} (score: {score:.3f})")

                    # Show output information
                    if results.get('clips'):
                        self.log_message(f"\n📹 Video clips saved to: {results['output_info']['clips_dir']}")

                    if results.get('thumbnails'):
                        self.log_message(f"🖼️  Thumbnails saved to: {results['output_info']['thumbnails_dir']}")

                    self.search_results = matches

                    # Switch to results tab
                    self.notebook.select(3)  # Results tab

                else:
                    self.log_message("❌ No matches found. Try:")
                    self.log_message("   - Lowering the similarity threshold")
                    self.log_message("   - Using different search terms")
                    self.log_message("   - Checking if the content exists in the video")

            else:
                self.log_message(f"❌ Search failed: {results.get('error', 'Unknown error')}")

        except Exception as e:
            self.log_message(f"❌ Error processing results: {e}")

        finally:
            self.search_completed()

    def search_completed(self):
        """Reset UI after search completion."""
        self.is_searching = False
        self.search_button.config(state='normal')
        self.stop_search_button.config(state='disabled')
        self.progress_bar.stop()
        self.progress_var.set("Ready")

    def stop_video_search(self):
        """Stop video search."""
        self.is_searching = False
        self.log_message("⏹️ Search stopped by user")
        self.search_completed()

    def start_live_detection(self):
        """Start live detection."""
        query = self.live_query_var.get().strip()
        if not query:
            messagebox.showerror("Error", "Please enter a search query for live detection!")
            return

        if self.is_live_detecting:
            messagebox.showwarning("Warning", "Live detection is already running!")
            return

        try:
            camera_index = self.camera_var.get()
            detection_interval = self.live_interval_var.get()

            # Test camera access first
            test_cap = cv2.VideoCapture(camera_index)
            if not test_cap.isOpened():
                test_cap.release()
                messagebox.showerror("Error", f"Cannot access camera {camera_index}. Please check camera connection.")
                return
            test_cap.release()

            # Initialize live detector
            self.live_detector = LiveVideoDetector()

            # Start detection with proper error handling
            try:
                success = self.live_detector.start_detection(
                    query=query,
                    camera_index=camera_index,
                    similarity_threshold=0.2,
                    detection_interval=detection_interval,
                    max_results=20
                )

                if success:
                    self.is_live_detecting = True
                    self.start_live_button.config(state='disabled')
                    self.pause_live_button.config(state='normal')
                    self.stop_live_button.config(state='normal')
                    self.live_status_var.set("🔴 Live Detection Active")

                    self.log_live_message(f"▶️ Started live detection for '{query}'")
                    self.log_live_message(f"📹 Using camera {camera_index}")

                    # Start monitoring thread
                    self.live_detection_thread = threading.Thread(target=self.monitor_live_detection, daemon=True)
                    self.live_detection_thread.start()
                else:
                    messagebox.showerror("Error", "Failed to start live detection. Check camera connection.")

            except AttributeError:
                # Fallback for older LiveVideoDetector interface
                self.start_simple_live_detection(query, camera_index, detection_interval)

        except Exception as e:
            messagebox.showerror("Error", f"Failed to start live detection: {e}")
            traceback.print_exc()

    def start_simple_live_detection(self, query, camera_index, detection_interval):
        """Fallback simple live detection implementation."""
        try:
            self.live_detector = LiveVideoDetector()

            # Initialize models
            self.live_detector.initialize_models()

            # Set up detection parameters
            self.live_detector.query = query
            self.live_detector.similarity_threshold = 0.2
            self.live_detector.detection_interval = detection_interval

            # Start camera
            self.live_detector.cap = cv2.VideoCapture(camera_index)
            if not self.live_detector.cap.isOpened():
                messagebox.showerror("Error", f"Cannot access camera {camera_index}")
                return

            self.live_detector.is_running = True
            self.is_live_detecting = True

            # Update UI
            self.start_live_button.config(state='disabled')
            self.pause_live_button.config(state='normal')
            self.stop_live_button.config(state='normal')
            self.live_status_var.set("🔴 Live Detection Active")

            self.log_live_message(f"▶️ Started simple live detection for '{query}'")
            self.log_live_message(f"📹 Using camera {camera_index}")

            # Start monitoring thread
            self.live_detection_thread = threading.Thread(target=self.simple_live_detection_loop, daemon=True)
            self.live_detection_thread.start()

        except Exception as e:
            messagebox.showerror("Error", f"Failed to start simple live detection: {e}")
            traceback.print_exc()

    def simple_live_detection_loop(self):
        """Simple live detection loop for fallback."""
        detection_count = 0
        last_detection_time = 0

        while self.is_live_detecting and self.live_detector and self.live_detector.cap:
            try:
                current_time = time.time()

                if current_time - last_detection_time >= self.live_detector.detection_interval:
                    ret, frame = self.live_detector.cap.read()
                    if not ret:
                        break

                    # Convert BGR to RGB
                    frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)

                    # Perform detection
                    if self.live_detector.clip_matcher:
                        from PIL import Image
                        pil_image = Image.fromarray(frame_rgb)
                        similarity = self.live_detector.clip_matcher.calculate_similarity(
                            pil_image, self.live_detector.query
                        )

                        if similarity >= self.live_detector.similarity_threshold:
                            detection_count += 1
                            timestamp = time.strftime("%H:%M:%S", time.localtime(current_time))
                            message = f"🎯 Detection #{detection_count} at {timestamp} (score: {similarity:.3f})"
                            self.root.after(0, self.log_live_message, message)

                    last_detection_time = current_time

                time.sleep(0.1)

            except Exception as e:
                error_msg = f"❌ Detection error: {e}"
                self.root.after(0, self.log_live_message, error_msg)
                break

    def pause_live_detection(self):
        """Pause/resume live detection."""
        try:
            if self.live_detector and hasattr(self.live_detector, 'is_paused'):
                if self.live_detector.is_paused:
                    if hasattr(self.live_detector, 'resume_detection'):
                        self.live_detector.resume_detection()
                    else:
                        self.live_detector.is_paused = False
                    self.pause_live_button.config(text="⏸️ Pause")
                    self.live_status_var.set("🔴 Live Detection Active")
                    self.log_live_message("▶️ Detection resumed")
                else:
                    if hasattr(self.live_detector, 'pause_detection'):
                        self.live_detector.pause_detection()
                    else:
                        self.live_detector.is_paused = True
                    self.pause_live_button.config(text="▶️ Resume")
                    self.live_status_var.set("⏸️ Detection Paused")
                    self.log_live_message("⏸️ Detection paused")
            else:
                messagebox.showwarning("Warning", "No active detection to pause/resume")
        except Exception as e:
            self.log_live_message(f"❌ Error pausing detection: {e}")

    def stop_live_detection(self):
        """Stop live detection."""
        try:
            self.is_live_detecting = False

            if self.live_detector:
                # Try different stop methods
                if hasattr(self.live_detector, 'stop_detection'):
                    self.live_detector.stop_detection()
                else:
                    self.live_detector.is_running = False
                    if hasattr(self.live_detector, 'cap') and self.live_detector.cap:
                        self.live_detector.cap.release()

                self.live_detector = None

            # Wait for thread to finish
            if self.live_detection_thread and self.live_detection_thread.is_alive():
                self.live_detection_thread.join(timeout=2.0)

            # Reset UI
            self.start_live_button.config(state='normal')
            self.pause_live_button.config(state='disabled', text="⏸️ Pause")
            self.stop_live_button.config(state='disabled')
            self.live_status_var.set("⏹️ Detection Stopped")

            self.log_live_message("⏹️ Live detection stopped")

        except Exception as e:
            self.log_live_message(f"❌ Error stopping detection: {e}")
            # Force reset UI even if there's an error
            self.is_live_detecting = False
            self.start_live_button.config(state='normal')
            self.pause_live_button.config(state='disabled', text="⏸️ Pause")
            self.stop_live_button.config(state='disabled')
            self.live_status_var.set("⏹️ Detection Stopped")

    def monitor_live_detection(self):
        """Monitor live detection results."""
        detection_count = 0

        while self.is_live_detecting and self.live_detector:
            try:
                # Try to get latest detection result
                result = None
                if hasattr(self.live_detector, 'get_latest_detection'):
                    result = self.live_detector.get_latest_detection()
                elif hasattr(self.live_detector, 'detection_results') and not self.live_detector.detection_results.empty():
                    try:
                        result = self.live_detector.detection_results.get_nowait()
                    except queue.Empty:
                        pass

                if result:
                    # Handle different result formats
                    detected = False
                    score = 0.0
                    timestamp_str = time.strftime("%H:%M:%S")

                    if isinstance(result, dict):
                        if 'results' in result:
                            detected = result['results'].get('detected', False)
                            score = result['results'].get('similarity_score', 0.0)
                            if 'timestamp' in result:
                                timestamp_str = time.strftime("%H:%M:%S", time.localtime(result['timestamp']))
                        else:
                            detected = result.get('detected', False)
                            score = result.get('similarity_score', 0.0)

                    if detected:
                        detection_count += 1
                        message = f"🎯 Detection #{detection_count} at {timestamp_str} (score: {score:.3f})"

                        # Check for objects
                        objects = None
                        if isinstance(result, dict):
                            if 'results' in result:
                                objects = result['results'].get('objects', [])
                            else:
                                objects = result.get('objects', [])

                        if objects:
                            objects_count = len(objects)
                            message += f" - {objects_count} objects detected"

                        self.root.after(0, self.log_live_message, message)

                time.sleep(0.5)

            except Exception as e:
                error_msg = f"❌ Detection monitoring error: {e}"
                self.root.after(0, self.log_live_message, error_msg)
                time.sleep(1.0)  # Wait longer on error

        self.root.after(0, self.log_live_message, "📊 Detection monitoring stopped")

    def save_settings(self):
        """Save current settings."""
        try:
            # Update configuration
            self.config.models.clip_model_name = self.clip_model_var.get()
            self.config.performance.enable_gpu = self.enable_gpu_var.get()
            self.config.performance.enable_parallel_processing = self.parallel_processing_var.get()
            self.config.processing.default_frame_interval = self.frame_interval_var.get()

            # Save to file
            if self.config.save_config():
                messagebox.showinfo("Success", "Settings saved successfully!")
                self.log_message("💾 Settings saved successfully")

                # Reinitialize search engine with new settings
                self.initialize_search_engine()
            else:
                messagebox.showerror("Error", "Failed to save settings!")

        except Exception as e:
            messagebox.showerror("Error", f"Failed to save settings: {e}")

    def open_results_folder(self):
        """Open results folder in file explorer."""
        try:
            output_dir = self.config.output.output_dir
            if os.path.exists(output_dir):
                if sys.platform == "win32":
                    os.startfile(output_dir)
                elif sys.platform == "darwin":
                    os.system(f"open '{output_dir}'")
                else:
                    os.system(f"xdg-open '{output_dir}'")
            else:
                messagebox.showwarning("Warning", f"Results folder not found: {output_dir}")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to open results folder: {e}")

    def clear_results(self):
        """Clear results display."""
        if messagebox.askyesno("Confirm", "Clear all results from display?"):
            self.results_text.delete(1.0, tk.END)
            self.live_results_text.delete(1.0, tk.END)
            self.search_results = []
            self.log_message("🗑️ Results cleared")

    def export_results(self):
        """Export results to file."""
        if not self.search_results:
            messagebox.showwarning("Warning", "No search results to export!")
            return

        filename = filedialog.asksaveasfilename(
            title="Export Results",
            defaultextension=".txt",
            filetypes=[("Text files", "*.txt"), ("CSV files", "*.csv"), ("All files", "*.*")]
        )

        if filename:
            try:
                with open(filename, 'w') as f:
                    f.write("AI Video Search Results\n")
                    f.write("=" * 50 + "\n\n")

                    for i, match in enumerate(self.search_results, 1):
                        timestamp = match['timestamp']
                        score = match['similarity_score']
                        frame_idx = match['frame_index']

                        minutes = int(timestamp // 60)
                        seconds = int(timestamp % 60)

                        f.write(f"{i:2d}. Frame {frame_idx:4d} at {minutes:02d}:{seconds:02d} (score: {score:.3f})\n")

                messagebox.showinfo("Success", f"Results exported to {filename}")
                self.log_message(f"💾 Results exported to {os.path.basename(filename)}")

            except Exception as e:
                messagebox.showerror("Error", f"Failed to export results: {e}")

    def log_message(self, message):
        """Log message to results text area."""
        try:
            if hasattr(self, 'results_text') and self.results_text:
                self.results_text.insert(tk.END, f"{message}\n")
                self.results_text.see(tk.END)
            else:
                print(f"LOG: {message}")  # Fallback to console
        except Exception as e:
            print(f"LOG ERROR: {e} - Message: {message}")

    def log_live_message(self, message):
        """Log message to live results text area."""
        try:
            if hasattr(self, 'live_results_text') and self.live_results_text:
                self.live_results_text.insert(tk.END, f"{message}\n")
                self.live_results_text.see(tk.END)
            else:
                print(f"LIVE LOG: {message}")  # Fallback to console
        except Exception as e:
            print(f"LIVE LOG ERROR: {e} - Message: {message}")

    def on_closing(self):
        """Handle application closing."""
        try:
            # Check if any operations are running
            operations_running = []
            if self.is_searching:
                operations_running.append("video search")
            if self.is_live_detecting:
                operations_running.append("live detection")

            if operations_running:
                operations_str = " and ".join(operations_running)
                if messagebox.askyesno("Confirm Exit", f"{operations_str.title()} is running. Stop and exit?"):
                    # Stop all operations
                    if self.is_live_detecting:
                        self.stop_live_detection()
                    if self.is_searching:
                        self.is_searching = False

                    # Give a moment for cleanup
                    self.root.after(500, self.root.destroy)
                else:
                    return
            else:
                self.root.destroy()

        except Exception as e:
            print(f"Error during closing: {e}")
            self.root.destroy()  # Force close on error


def main():
    """Main function to run the desktop application."""
    print("🚀 Starting AI Video Search Desktop Application...")

    try:
        # Check if tkinter is available
        try:
            import tkinter as tk
        except ImportError:
            print("❌ Error: tkinter is not available")
            print("💡 On Linux, install with: sudo apt-get install python3-tk")
            return

        # Create main window
        root = tk.Tk()

        # Set minimum size
        root.minsize(800, 600)

        # Create application
        app = VideoSearchGUI(root)

        # Check if app was created successfully
        if app.config is None:
            print("❌ Failed to initialize application")
            return

        # Handle window closing
        root.protocol("WM_DELETE_WINDOW", app.on_closing)

        # Center window on screen
        try:
            root.update_idletasks()
            width = root.winfo_width()
            height = root.winfo_height()
            x = (root.winfo_screenwidth() // 2) - (width // 2)
            y = (root.winfo_screenheight() // 2) - (height // 2)
            root.geometry(f"{width}x{height}+{x}+{y}")
        except Exception as e:
            print(f"⚠️ Warning: Could not center window: {e}")

        print("✅ Desktop application started successfully!")
        print("💡 Use the GUI to search videos and perform live detection")

        # Start the GUI event loop
        try:
            root.mainloop()
        except KeyboardInterrupt:
            print("\n👋 Application stopped by user")
        except Exception as e:
            print(f"❌ Application error: {e}")
            traceback.print_exc()

    except Exception as e:
        print(f"❌ Failed to start application: {e}")
        traceback.print_exc()
        input("Press Enter to exit...")  # Keep console open on Windows


if __name__ == "__main__":
    main()
